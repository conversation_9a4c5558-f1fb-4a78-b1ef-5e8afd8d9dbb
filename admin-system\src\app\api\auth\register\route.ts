import { NextRequest, NextResponse } from 'next/server';
import { <PERSON><PERSON><PERSON>ana<PERSON>, SessionManager, JWTUtils } from '@/lib/auth';
import { ActivationCodeManager } from '@/lib/activation-codes';
import {
  successResponse,
  errorResponse,
  validateRequest,
  validators,
  logAuditEvent,
  getClientIP,
  rateLimit
} from '@/lib/api-utils';



export async function POST(request: NextRequest) {
  try {
    // Rate limiting disabled for development
    // const clientIP = getClientIP(request);
    // if (!rateLimit(`register:${clientIP}`, 3, 3600000)) { // 3 attempts per hour
    //   return errorResponse('Too many registration attempts. Please try again later.', 429);
    // }

    // 安全地解析请求体
    let body;
    try {
      const text = await request.text();

      if (!text || text.trim() === '') {
        return errorResponse('请求体不能为空', 400, 'EMPTY_BODY');
      }
      body = JSON.parse(text);
    } catch (error) {
      console.error('注册API JSON解析错误:', error);
      return errorResponse('请求格式错误，请发送有效的JSON数据', 400, 'INVALID_JSON');
    }

    // Validate request
    const { isValid, errors, data } = validateRequest<{
      username: string;
      email: string;
      password: string;
      full_name?: string;
      activation_code: string;
    }>(body, {
      username: (value) => {
        const required = validators.required(value);
        if (required) return required;
        const minLength = validators.minLength(3)(value);
        if (minLength) return minLength;
        const maxLength = validators.maxLength(20)(value); // 限制为20位以下
        if (maxLength) return maxLength;
        // Check username format (英文数字only)
        if (!/^[a-zA-Z0-9]+$/.test(value)) {
          return '账号只能包含英文字母和数字';
        }
        return null;
      },
      email: (value) => {
        const required = validators.required(value);
        if (required) return required;
        return validators.email(value);
      },
      password: (value) => {
        const required = validators.required(value);
        if (required) return required;
        const minLength = validators.minLength(8)(value);
        if (minLength) return minLength;
        // Check password strength
        if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
          return '密码必须包含至少一个小写字母、一个大写字母和一个数字';
        }
        return null;
      },
      full_name: validators.maxLength(100),
      activation_code: (value) => {
        const required = validators.required(value);
        if (required) return required;
        // 验证激活码格式（通常是16-32位字符）
        if (!/^[A-Z0-9]{16,32}$/.test(value)) {
          return '激活码格式不正确';
        }
        return null;
      },
    });

    if (!isValid) {
      return errorResponse('验证失败', 400, 'VALIDATION_ERROR');
    }

    // Check if username already exists
    const existingUserByUsername = await UserManager.getUserByUsername(data.username);
    if (existingUserByUsername) {
      return errorResponse('账号已存在', 409, 'USERNAME_EXISTS');
    }

    // Check if email already exists
    const existingUserByEmail = await UserManager.getUserByEmail(data.email);
    if (existingUserByEmail) {
      return errorResponse('邮箱已存在', 409, 'EMAIL_EXISTS');
    }

    // 验证激活码
    const activationCode = await ActivationCodeManager.getActivationCodeByCode(data.activation_code);
    if (!activationCode) {
      return errorResponse('激活码不存在', 400, 'INVALID_ACTIVATION_CODE');
    }

    if (activationCode.status !== 'active') {
      return errorResponse('激活码已失效', 400, 'ACTIVATION_CODE_EXPIRED');
    }

    if (activationCode.expires_at && new Date(activationCode.expires_at) < new Date()) {
      return errorResponse('激活码已过期', 400, 'ACTIVATION_CODE_EXPIRED');
    }

    // Create user
    const user = await UserManager.createUser({
      username: data.username,
      email: data.email,
      password: data.password,
      full_name: data.full_name || data.username, // 默认使用账号作为用户名
      role: 'user', // Default role
      registration_source: 'mobile', // 标记为移动端注册
    });

    // 使用激活码
    try {
      await ActivationCodeManager.useActivationCode(data.activation_code, user.id);
    } catch (error) {
      // 如果激活码使用失败，需要删除已创建的用户
      await UserManager.deleteUser(user.id);
      return errorResponse('激活码使用失败', 500, 'ACTIVATION_CODE_USE_FAILED');
    }

    // Create session
    const sessionId = await SessionManager.createSession(user.id);

    // Generate JWT token
    const token = JWTUtils.sign({
      userId: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      sessionId,
    });

    // Log successful registration
    await logAuditEvent({
      userId: user.id,
      action: 'USER_REGISTERED',
      details: {
        username: user.username,
        email: user.email,
        sessionId,
      },
      ipAddress: clientIP,
      userAgent: request.headers.get('user-agent') || undefined,
    });

    return successResponse({
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        full_name: user.full_name,
        role: user.role,
        avatar_url: user.avatar_url,
        created_at: user.created_at,
      },
      token,
      sessionId,
    }, '注册成功');

  } catch (error) {
    console.error('Registration error:', error);
    return errorResponse('服务器内部错误', 500);
  }
}
