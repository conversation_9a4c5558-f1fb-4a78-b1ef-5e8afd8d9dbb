<template>
  <view class="user-status-container">
    <!-- 登录状态 -->
    <view v-if="!isLoggedIn" class="login-prompt">
      <text class="prompt-text">请先登录</text>
      <button class="login-btn" @tap="goToLogin">立即登录</button>
    </view>

    <!-- 用户信息 -->
    <view v-else class="user-info-container">
      <!-- 用户头像和基本信息 -->
      <view class="user-basic-info">
        <image class="user-avatar" :src="userAvatar" mode="aspectFit"></image>
        <view class="user-details">
          <text class="user-name">{{ userDisplayName }}</text>
          <text class="user-email">{{ userEmail }}</text>
        </view>
      </view>

      <!-- VIP状态 -->
      <view v-if="showVipStatus" class="vip-status">
        <view v-if="isVip" class="vip-info">
          <view class="vip-badge" :class="'vip-' + vipLevel">
            <text class="vip-text">{{ getVipLevelText() }}</text>
          </view>
          <text class="vip-expires">{{ getVipExpiresText() }}</text>
        </view>
        <view v-else class="non-vip">
          <text class="non-vip-text">普通用户</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view v-if="showActions" class="action-buttons">
        <button class="action-btn" @tap="refreshUserInfo">刷新</button>
        <button class="action-btn logout-btn" @tap="handleLogout">退出</button>
      </view>
    </view>
  </view>
</template>

<script>
import { AuthHelper } from '@/utils/auth-helper.js';
import { ToastManager } from '@/utils/toast-manager.js';

export default {
  name: 'UserStatus',
  
  props: {
    // 是否显示VIP状态
    showVipStatus: {
      type: Boolean,
      default: true
    },
    // 是否显示操作按钮
    showActions: {
      type: Boolean,
      default: false
    },
    // 自定义样式类名
    customClass: {
      type: String,
      default: ''
    }
  },

  data() {
    return {
      userInfo: null,
      loading: false
    };
  },

  computed: {
    // 是否已登录
    isLoggedIn() {
      return AuthHelper.isLoggedIn() && !!this.userInfo;
    },

    // 用户显示名称
    userDisplayName() {
      if (!this.userInfo) return '';
      return this.userInfo.full_name || this.userInfo.username || '用户';
    },

    // 用户邮箱
    userEmail() {
      if (!this.userInfo) return '';
      return this.userInfo.email || '';
    },

    // 用户头像
    userAvatar() {
      if (!this.userInfo || !this.userInfo.avatar_url) {
        return '/static/figma-assets/assets/b0b4af09-8098-4d18-aed3-71537580e1af.png';
      }
      return this.userInfo.avatar_url;
    },

    // VIP等级
    vipLevel() {
      if (!this.userInfo) return 'none';
      return this.userInfo.vip_level || 'none';
    },

    // 是否为VIP
    isVip() {
      return this.vipLevel && this.vipLevel !== 'none';
    },

    // VIP过期时间
    vipExpiresAt() {
      if (!this.userInfo) return null;
      return this.userInfo.vip_expires_at;
    }
  },

  mounted() {
    this.loadUserInfo();
    
    // 监听登录状态变化
    this.unsubscribe = AuthHelper.onAuthStateChange((state) => {
      if (state.isLoggedIn) {
        this.userInfo = state.userInfo;
      } else {
        this.userInfo = null;
      }
    });
  },

  beforeDestroy() {
    // 取消监听
    if (this.unsubscribe) {
      this.unsubscribe();
    }
  },

  methods: {
    // 加载用户信息
    async loadUserInfo() {
      if (!AuthHelper.isLoggedIn()) {
        this.userInfo = null;
        return;
      }

      try {
        this.loading = true;
        const userInfo = AuthHelper.getCurrentUser();
        
        if (userInfo) {
          this.userInfo = userInfo;
        } else {
          // 尝试从服务器获取
          const result = await AuthHelper.refreshUserInfo();
          if (result.success) {
            this.userInfo = result.user;
          }
        }
      } catch (error) {
        console.error('加载用户信息失败:', error);
      } finally {
        this.loading = false;
      }
    },

    // 刷新用户信息
    async refreshUserInfo() {
      try {
        this.loading = true;
        const result = await AuthHelper.refreshUserInfo();
        if (result.success) {
          this.userInfo = result.user;
          ToastManager.success('刷新成功');
        } else {
          ToastManager.error('刷新失败');
        }
      } catch (error) {
        console.error('刷新用户信息失败:', error);
        ToastManager.error('刷新失败');
      } finally {
        this.loading = false;
      }
    },

    // 跳转到登录页
    goToLogin() {
      AuthHelper.redirectToLogin();
    },

    // 处理登出
    async handleLogout() {
      try {
        const confirmed = await ToastManager.confirm({
          title: '提示',
          content: '确定要退出登录吗？'
        });

        if (confirmed) {
          const result = await AuthHelper.logout();
          if (result.success) {
            this.userInfo = null;
            this.$emit('logout');
          }
        }
      } catch (error) {
        console.error('退出登录失败:', error);
        ToastManager.error('退出失败');
      }
    },

    // 获取VIP等级文本
    getVipLevelText() {
      const levelMap = {
        v1: 'V1',
        v2: 'V2',
        v3: 'V3',
        v4: 'V4'
      };
      return levelMap[this.vipLevel] || 'VIP';
    },

    // 获取VIP过期文本
    getVipExpiresText() {
      if (!this.vipExpiresAt) return '';
      
      const expiresDate = new Date(this.vipExpiresAt);
      const now = new Date();
      
      if (expiresDate < now) {
        return '已过期';
      }
      
      const diffTime = expiresDate - now;
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      
      if (diffDays <= 7) {
        return `${diffDays}天后过期`;
      }
      
      return `${expiresDate.toLocaleDateString('zh-CN')}过期`;
    }
  }
};
</script>

<style scoped>
.user-status-container {
  width: 100%;
}

.login-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;
}

.prompt-text {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.login-btn {
  background: #007aff;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
}

.user-info-container {
  padding: 15px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-basic-info {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 25px;
  margin-right: 12px;
}

.user-details {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.user-email {
  display: block;
  font-size: 12px;
  color: #666;
}

.vip-status {
  margin-bottom: 15px;
}

.vip-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.vip-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.vip-v1 {
  background: #e3f2fd;
  color: #1976d2;
}

.vip-v2 {
  background: #f3e5f5;
  color: #7b1fa2;
}

.vip-v3 {
  background: #fff3e0;
  color: #f57c00;
}

.vip-v4 {
  background: #ffebee;
  color: #d32f2f;
}

.vip-text {
  font-size: 12px;
}

.vip-expires {
  font-size: 11px;
  color: #666;
}

.non-vip-text {
  font-size: 12px;
  color: #999;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.action-btn {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  color: #333;
}

.logout-btn {
  background: #ff4757;
  color: white;
  border-color: #ff4757;
}
</style>
